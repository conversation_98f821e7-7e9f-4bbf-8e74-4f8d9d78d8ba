[2025-07-30 04:35:45] local.ERROR: Please provide a valid cache path. {"exception":"[object] (InvalidArgumentException(code: 0): Please provide a valid cache path. at E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Compiler.php:36)
[stacktrace]
#0 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\View\\ViewServiceProvider.php(88): Illuminate\\View\\Compilers\\Compiler->__construct(Object(Illuminate\\Filesystem\\Filesystem), false)
#1 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(873): Illuminate\\View\\ViewServiceProvider->Illuminate\\View\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(758): Illuminate\\Container\\Container->build(Object(Closure))
#3 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(853): Illuminate\\Container\\Container->resolve('blade.compiler', Array, true)
#4 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Foundation\\Application->resolve('blade.compiler', Array)
#5 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(838): Illuminate\\Container\\Container->make('blade.compiler', Array)
#6 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1419): Illuminate\\Foundation\\Application->make('blade.compiler')
#7 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\View\\ViewServiceProvider.php(150): Illuminate\\Container\\Container->offsetGet('blade.compiler')
#8 [internal function]: Illuminate\\View\\ViewServiceProvider->Illuminate\\View\\{closure}()
#9 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\EngineResolver.php(55): call_user_func(Object(Closure))
#10 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\facade\\ignition\\src\\IgnitionServiceProvider.php(467): Illuminate\\View\\Engines\\EngineResolver->resolve('blade')
#11 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\facade\\ignition\\src\\IgnitionServiceProvider.php(158): Facade\\Ignition\\IgnitionServiceProvider->hasCustomViewEnginesRegistered()
#12 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\facade\\ignition\\src\\IgnitionServiceProvider.php(101): Facade\\Ignition\\IgnitionServiceProvider->registerViewEngines()
#13 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Facade\\Ignition\\IgnitionServiceProvider->boot()
#14 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#16 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#17 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#18 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(926): Illuminate\\Container\\Container->call(Array)
#19 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(907): Illuminate\\Foundation\\Application->bootProvider(Object(Facade\\Ignition\\IgnitionServiceProvider))
#20 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Facade\\Ignition\\IgnitionServiceProvider), 15)
#21 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(906): array_walk(Array, Object(Closure))
#22 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#23 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(237): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#24 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(153): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#25 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(137): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#26 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#27 E:\\kumaran\\Primus-LandD-Packaging\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#28 E:\\kumaran\\Primus-LandD-Packaging\\server.php(21): require_once('E:\\\\kumaran\\\\Prim...')
#29 {main}
"} 
[2025-07-30 04:35:45] local.ERROR: Please provide a valid cache path. {"exception":"[object] (InvalidArgumentException(code: 0): Please provide a valid cache path. at E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Compiler.php:36)
[stacktrace]
#0 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\View\\ViewServiceProvider.php(88): Illuminate\\View\\Compilers\\Compiler->__construct(Object(Illuminate\\Filesystem\\Filesystem), false)
#1 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(873): Illuminate\\View\\ViewServiceProvider->Illuminate\\View\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(758): Illuminate\\Container\\Container->build(Object(Closure))
#3 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(853): Illuminate\\Container\\Container->resolve('blade.compiler', Array, true)
#4 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Foundation\\Application->resolve('blade.compiler', Array)
#5 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(838): Illuminate\\Container\\Container->make('blade.compiler', Array)
#6 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1419): Illuminate\\Foundation\\Application->make('blade.compiler')
#7 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\View\\ViewServiceProvider.php(150): Illuminate\\Container\\Container->offsetGet('blade.compiler')
#8 [internal function]: Illuminate\\View\\ViewServiceProvider->Illuminate\\View\\{closure}()
#9 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\EngineResolver.php(55): call_user_func(Object(Closure))
#10 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Factory.php(310): Illuminate\\View\\Engines\\EngineResolver->resolve('blade')
#11 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Factory.php(274): Illuminate\\View\\Factory->getEngineFromPath('E:\\\\kumaran\\\\Prim...')
#12 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Factory.php(146): Illuminate\\View\\Factory->viewInstance('errors::500', 'E:\\\\kumaran\\\\Prim...', Array)
#13 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(85): Illuminate\\View\\Factory->make('errors::500', Array)
#14 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(582): Illuminate\\Routing\\ResponseFactory->view('errors::500', Array, 500, Array)
#15 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(490): Illuminate\\Foundation\\Exceptions\\Handler->renderHttpException(Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException))
#16 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(356): Illuminate\\Foundation\\Exceptions\\Handler->prepareResponse(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException))
#17 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(427): Illuminate\\Foundation\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(InvalidArgumentException))
#18 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(115): Illuminate\\Foundation\\Http\\Kernel->renderException(Object(Illuminate\\Http\\Request), Object(InvalidArgumentException))
#19 E:\\kumaran\\Primus-LandD-Packaging\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#20 E:\\kumaran\\Primus-LandD-Packaging\\server.php(21): require_once('E:\\\\kumaran\\\\Prim...')
#21 {main}
"} 
[2025-07-30 04:35:46] local.ERROR: Uncaught InvalidArgumentException: Please provide a valid cache path. in E:\kumaran\Primus-LandD-Packaging\vendor\laravel\framework\src\Illuminate\View\Compilers\Compiler.php:36
Stack trace:
#0 E:\kumaran\Primus-LandD-Packaging\vendor\laravel\framework\src\Illuminate\View\ViewServiceProvider.php(88): Illuminate\View\Compilers\Compiler->__construct(Object(Illuminate\Filesystem\Filesystem), false)
#1 E:\kumaran\Primus-LandD-Packaging\vendor\laravel\framework\src\Illuminate\Container\Container.php(873): Illuminate\View\ViewServiceProvider->Illuminate\View\{closure}(Object(Illuminate\Foundation\Application), Array)
#2 E:\kumaran\Primus-LandD-Packaging\vendor\laravel\framework\src\Illuminate\Container\Container.php(758): Illuminate\Container\Container->build(Object(Closure))
#3 E:\kumaran\Primus-LandD-Packaging\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(853): Illuminate\Container\Container->resolve('blade.compiler', Array, true)
#4 E:\kumaran\Primus-LandD-Packaging\vendor\laravel\framework\src\Illuminate\Container\Container.php(694): Illuminate\Foundation\Application->resolve('blade.compiler', Array)
#5 E:\kumaran\Primus-LandD-Packaging\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(838): Illuminate\Container\Container->make('blade.compiler', Array)
#6 E:\kumaran\Primus-LandD-Packaging\vendor\laravel\framework\src\Illuminate\Container\Container.php(1419): Illuminate\Foundation\Application->make('blade.compiler')
#7 E:\kumaran\Primus-LandD-Packaging\vendor\laravel\framework\src\Illuminate\View\ViewServiceProvider.php(150): Illuminate\Container\Container->offsetGet('blade.compiler')
#8 [internal function]: Illuminate\View\ViewServiceProvider->Illuminate\View\{closure}()
#9 E:\kumaran\Primus-LandD-Packaging\vendor\laravel\framework\src\Illuminate\View\Engines\EngineResolver.php(55): call_user_func(Object(Closure))
#10 E:\kumaran\Primus-LandD-Packaging\vendor\laravel\framework\src\Illuminate\View\Factory.php(310): Illuminate\View\Engines\EngineResolver->resolve('blade')
#11 E:\kumaran\Primus-LandD-Packaging\vendor\laravel\framework\src\Illuminate\View\Factory.php(274): Illuminate\View\Factory->getEngineFromPath('E:\\kumaran\\Prim...')
#12 E:\kumaran\Primus-LandD-Packaging\vendor\laravel\framework\src\Illuminate\View\Factory.php(146): Illuminate\View\Factory->viewInstance('errors::500', 'E:\\kumaran\\Prim...', Array)
#13 E:\kumaran\Primus-LandD-Packaging\vendor\laravel\framework\src\Illuminate\Routing\ResponseFactory.php(85): Illuminate\View\Factory->make('errors::500', Array)
#14 E:\kumaran\Primus-LandD-Packaging\vendor\laravel\framework\src\Illuminate\Foundation\Exceptions\Handler.php(582): Illuminate\Routing\ResponseFactory->view('errors::500', Array, 500, Array)
#15 E:\kumaran\Primus-LandD-Packaging\vendor\laravel\framework\src\Illuminate\Foundation\Exceptions\Handler.php(490): Illuminate\Foundation\Exceptions\Handler->renderHttpException(Object(Symfony\Component\HttpKernel\Exception\HttpException))
#16 E:\kumaran\Primus-LandD-Packaging\vendor\laravel\framework\src\Illuminate\Foundation\Exceptions\Handler.php(356): Illuminate\Foundation\Exceptions\Handler->prepareResponse(Object(Illuminate\Http\Request), Object(Symfony\Component\HttpKernel\Exception\HttpException))
#17 E:\kumaran\Primus-LandD-Packaging\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(196): Illuminate\Foundation\Exceptions\Handler->render(Object(Illuminate\Http\Request), Object(InvalidArgumentException))
#18 E:\kumaran\Primus-LandD-Packaging\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(173): Illuminate\Foundation\Bootstrap\HandleExceptions->renderHttpResponse(Object(InvalidArgumentException))
#19 [internal function]: Illuminate\Foundation\Bootstrap\HandleExceptions->handleException(Object(InvalidArgumentException))
#20 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught InvalidArgumentException: Please provide a valid cache path. in E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Compiler.php:36
Stack trace:
#0 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\View\\ViewServiceProvider.php(88): Illuminate\\View\\Compilers\\Compiler->__construct(Object(Illuminate\\Filesystem\\Filesystem), false)
#1 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(873): Illuminate\\View\\ViewServiceProvider->Illuminate\\View\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(758): Illuminate\\Container\\Container->build(Object(Closure))
#3 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(853): Illuminate\\Container\\Container->resolve('blade.compiler', Array, true)
#4 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Foundation\\Application->resolve('blade.compiler', Array)
#5 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(838): Illuminate\\Container\\Container->make('blade.compiler', Array)
#6 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1419): Illuminate\\Foundation\\Application->make('blade.compiler')
#7 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\View\\ViewServiceProvider.php(150): Illuminate\\Container\\Container->offsetGet('blade.compiler')
#8 [internal function]: Illuminate\\View\\ViewServiceProvider->Illuminate\\View\\{closure}()
#9 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\EngineResolver.php(55): call_user_func(Object(Closure))
#10 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Factory.php(310): Illuminate\\View\\Engines\\EngineResolver->resolve('blade')
#11 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Factory.php(274): Illuminate\\View\\Factory->getEngineFromPath('E:\\\\kumaran\\\\Prim...')
#12 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Factory.php(146): Illuminate\\View\\Factory->viewInstance('errors::500', 'E:\\\\kumaran\\\\Prim...', Array)
#13 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(85): Illuminate\\View\\Factory->make('errors::500', Array)
#14 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(582): Illuminate\\Routing\\ResponseFactory->view('errors::500', Array, 500, Array)
#15 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(490): Illuminate\\Foundation\\Exceptions\\Handler->renderHttpException(Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException))
#16 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(356): Illuminate\\Foundation\\Exceptions\\Handler->prepareResponse(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException))
#17 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(196): Illuminate\\Foundation\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(InvalidArgumentException))
#18 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(173): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->renderHttpResponse(Object(InvalidArgumentException))
#19 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(InvalidArgumentException))
#20 {main}
  thrown at E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Compiler.php:36)
[stacktrace]
#0 {main}
"} 
[2025-07-30 04:35:55] local.ERROR: Please provide a valid cache path. {"exception":"[object] (InvalidArgumentException(code: 0): Please provide a valid cache path. at E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Compiler.php:36)
[stacktrace]
#0 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\View\\ViewServiceProvider.php(88): Illuminate\\View\\Compilers\\Compiler->__construct(Object(Illuminate\\Filesystem\\Filesystem), false)
#1 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(873): Illuminate\\View\\ViewServiceProvider->Illuminate\\View\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(758): Illuminate\\Container\\Container->build(Object(Closure))
#3 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(853): Illuminate\\Container\\Container->resolve('blade.compiler', Array, true)
#4 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Foundation\\Application->resolve('blade.compiler', Array)
#5 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(838): Illuminate\\Container\\Container->make('blade.compiler', Array)
#6 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1419): Illuminate\\Foundation\\Application->make('blade.compiler')
#7 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\View\\ViewServiceProvider.php(150): Illuminate\\Container\\Container->offsetGet('blade.compiler')
#8 [internal function]: Illuminate\\View\\ViewServiceProvider->Illuminate\\View\\{closure}()
#9 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\EngineResolver.php(55): call_user_func(Object(Closure))
#10 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\facade\\ignition\\src\\IgnitionServiceProvider.php(467): Illuminate\\View\\Engines\\EngineResolver->resolve('blade')
#11 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\facade\\ignition\\src\\IgnitionServiceProvider.php(158): Facade\\Ignition\\IgnitionServiceProvider->hasCustomViewEnginesRegistered()
#12 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\facade\\ignition\\src\\IgnitionServiceProvider.php(101): Facade\\Ignition\\IgnitionServiceProvider->registerViewEngines()
#13 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Facade\\Ignition\\IgnitionServiceProvider->boot()
#14 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#16 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#17 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#18 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(926): Illuminate\\Container\\Container->call(Array)
#19 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(907): Illuminate\\Foundation\\Application->bootProvider(Object(Facade\\Ignition\\IgnitionServiceProvider))
#20 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Facade\\Ignition\\IgnitionServiceProvider), 15)
#21 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(906): array_walk(Array, Object(Closure))
#22 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#23 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(237): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#24 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(153): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#25 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(137): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#26 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#27 E:\\kumaran\\Primus-LandD-Packaging\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#28 E:\\kumaran\\Primus-LandD-Packaging\\server.php(21): require_once('E:\\\\kumaran\\\\Prim...')
#29 {main}
"} 
[2025-07-30 04:35:55] local.ERROR: Please provide a valid cache path. {"exception":"[object] (InvalidArgumentException(code: 0): Please provide a valid cache path. at E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Compiler.php:36)
[stacktrace]
#0 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\View\\ViewServiceProvider.php(88): Illuminate\\View\\Compilers\\Compiler->__construct(Object(Illuminate\\Filesystem\\Filesystem), false)
#1 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(873): Illuminate\\View\\ViewServiceProvider->Illuminate\\View\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(758): Illuminate\\Container\\Container->build(Object(Closure))
#3 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(853): Illuminate\\Container\\Container->resolve('blade.compiler', Array, true)
#4 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Foundation\\Application->resolve('blade.compiler', Array)
#5 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(838): Illuminate\\Container\\Container->make('blade.compiler', Array)
#6 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1419): Illuminate\\Foundation\\Application->make('blade.compiler')
#7 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\View\\ViewServiceProvider.php(150): Illuminate\\Container\\Container->offsetGet('blade.compiler')
#8 [internal function]: Illuminate\\View\\ViewServiceProvider->Illuminate\\View\\{closure}()
#9 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\EngineResolver.php(55): call_user_func(Object(Closure))
#10 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Factory.php(310): Illuminate\\View\\Engines\\EngineResolver->resolve('blade')
#11 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Factory.php(274): Illuminate\\View\\Factory->getEngineFromPath('E:\\\\kumaran\\\\Prim...')
#12 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Factory.php(146): Illuminate\\View\\Factory->viewInstance('errors::500', 'E:\\\\kumaran\\\\Prim...', Array)
#13 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(85): Illuminate\\View\\Factory->make('errors::500', Array)
#14 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(582): Illuminate\\Routing\\ResponseFactory->view('errors::500', Array, 500, Array)
#15 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(490): Illuminate\\Foundation\\Exceptions\\Handler->renderHttpException(Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException))
#16 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(356): Illuminate\\Foundation\\Exceptions\\Handler->prepareResponse(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException))
#17 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(427): Illuminate\\Foundation\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(InvalidArgumentException))
#18 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(115): Illuminate\\Foundation\\Http\\Kernel->renderException(Object(Illuminate\\Http\\Request), Object(InvalidArgumentException))
#19 E:\\kumaran\\Primus-LandD-Packaging\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#20 E:\\kumaran\\Primus-LandD-Packaging\\server.php(21): require_once('E:\\\\kumaran\\\\Prim...')
#21 {main}
"} 
[2025-07-30 04:35:55] local.ERROR: Uncaught InvalidArgumentException: Please provide a valid cache path. in E:\kumaran\Primus-LandD-Packaging\vendor\laravel\framework\src\Illuminate\View\Compilers\Compiler.php:36
Stack trace:
#0 E:\kumaran\Primus-LandD-Packaging\vendor\laravel\framework\src\Illuminate\View\ViewServiceProvider.php(88): Illuminate\View\Compilers\Compiler->__construct(Object(Illuminate\Filesystem\Filesystem), false)
#1 E:\kumaran\Primus-LandD-Packaging\vendor\laravel\framework\src\Illuminate\Container\Container.php(873): Illuminate\View\ViewServiceProvider->Illuminate\View\{closure}(Object(Illuminate\Foundation\Application), Array)
#2 E:\kumaran\Primus-LandD-Packaging\vendor\laravel\framework\src\Illuminate\Container\Container.php(758): Illuminate\Container\Container->build(Object(Closure))
#3 E:\kumaran\Primus-LandD-Packaging\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(853): Illuminate\Container\Container->resolve('blade.compiler', Array, true)
#4 E:\kumaran\Primus-LandD-Packaging\vendor\laravel\framework\src\Illuminate\Container\Container.php(694): Illuminate\Foundation\Application->resolve('blade.compiler', Array)
#5 E:\kumaran\Primus-LandD-Packaging\vendor\laravel\framework\src\Illuminate\Foundation\Application.php(838): Illuminate\Container\Container->make('blade.compiler', Array)
#6 E:\kumaran\Primus-LandD-Packaging\vendor\laravel\framework\src\Illuminate\Container\Container.php(1419): Illuminate\Foundation\Application->make('blade.compiler')
#7 E:\kumaran\Primus-LandD-Packaging\vendor\laravel\framework\src\Illuminate\View\ViewServiceProvider.php(150): Illuminate\Container\Container->offsetGet('blade.compiler')
#8 [internal function]: Illuminate\View\ViewServiceProvider->Illuminate\View\{closure}()
#9 E:\kumaran\Primus-LandD-Packaging\vendor\laravel\framework\src\Illuminate\View\Engines\EngineResolver.php(55): call_user_func(Object(Closure))
#10 E:\kumaran\Primus-LandD-Packaging\vendor\laravel\framework\src\Illuminate\View\Factory.php(310): Illuminate\View\Engines\EngineResolver->resolve('blade')
#11 E:\kumaran\Primus-LandD-Packaging\vendor\laravel\framework\src\Illuminate\View\Factory.php(274): Illuminate\View\Factory->getEngineFromPath('E:\\kumaran\\Prim...')
#12 E:\kumaran\Primus-LandD-Packaging\vendor\laravel\framework\src\Illuminate\View\Factory.php(146): Illuminate\View\Factory->viewInstance('errors::500', 'E:\\kumaran\\Prim...', Array)
#13 E:\kumaran\Primus-LandD-Packaging\vendor\laravel\framework\src\Illuminate\Routing\ResponseFactory.php(85): Illuminate\View\Factory->make('errors::500', Array)
#14 E:\kumaran\Primus-LandD-Packaging\vendor\laravel\framework\src\Illuminate\Foundation\Exceptions\Handler.php(582): Illuminate\Routing\ResponseFactory->view('errors::500', Array, 500, Array)
#15 E:\kumaran\Primus-LandD-Packaging\vendor\laravel\framework\src\Illuminate\Foundation\Exceptions\Handler.php(490): Illuminate\Foundation\Exceptions\Handler->renderHttpException(Object(Symfony\Component\HttpKernel\Exception\HttpException))
#16 E:\kumaran\Primus-LandD-Packaging\vendor\laravel\framework\src\Illuminate\Foundation\Exceptions\Handler.php(356): Illuminate\Foundation\Exceptions\Handler->prepareResponse(Object(Illuminate\Http\Request), Object(Symfony\Component\HttpKernel\Exception\HttpException))
#17 E:\kumaran\Primus-LandD-Packaging\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(196): Illuminate\Foundation\Exceptions\Handler->render(Object(Illuminate\Http\Request), Object(InvalidArgumentException))
#18 E:\kumaran\Primus-LandD-Packaging\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(173): Illuminate\Foundation\Bootstrap\HandleExceptions->renderHttpResponse(Object(InvalidArgumentException))
#19 [internal function]: Illuminate\Foundation\Bootstrap\HandleExceptions->handleException(Object(InvalidArgumentException))
#20 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught InvalidArgumentException: Please provide a valid cache path. in E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Compiler.php:36
Stack trace:
#0 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\View\\ViewServiceProvider.php(88): Illuminate\\View\\Compilers\\Compiler->__construct(Object(Illuminate\\Filesystem\\Filesystem), false)
#1 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(873): Illuminate\\View\\ViewServiceProvider->Illuminate\\View\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(758): Illuminate\\Container\\Container->build(Object(Closure))
#3 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(853): Illuminate\\Container\\Container->resolve('blade.compiler', Array, true)
#4 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Foundation\\Application->resolve('blade.compiler', Array)
#5 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(838): Illuminate\\Container\\Container->make('blade.compiler', Array)
#6 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1419): Illuminate\\Foundation\\Application->make('blade.compiler')
#7 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\View\\ViewServiceProvider.php(150): Illuminate\\Container\\Container->offsetGet('blade.compiler')
#8 [internal function]: Illuminate\\View\\ViewServiceProvider->Illuminate\\View\\{closure}()
#9 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\EngineResolver.php(55): call_user_func(Object(Closure))
#10 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Factory.php(310): Illuminate\\View\\Engines\\EngineResolver->resolve('blade')
#11 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Factory.php(274): Illuminate\\View\\Factory->getEngineFromPath('E:\\\\kumaran\\\\Prim...')
#12 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Factory.php(146): Illuminate\\View\\Factory->viewInstance('errors::500', 'E:\\\\kumaran\\\\Prim...', Array)
#13 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(85): Illuminate\\View\\Factory->make('errors::500', Array)
#14 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(582): Illuminate\\Routing\\ResponseFactory->view('errors::500', Array, 500, Array)
#15 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(490): Illuminate\\Foundation\\Exceptions\\Handler->renderHttpException(Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException))
#16 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(356): Illuminate\\Foundation\\Exceptions\\Handler->prepareResponse(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\HttpException))
#17 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(196): Illuminate\\Foundation\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(InvalidArgumentException))
#18 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(173): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->renderHttpResponse(Object(InvalidArgumentException))
#19 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(InvalidArgumentException))
#20 {main}
  thrown at E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Compiler.php:36)
[stacktrace]
#0 {main}
"} 
[2025-07-30 04:36:03] local.ERROR: Please provide a valid cache path. {"exception":"[object] (InvalidArgumentException(code: 0): Please provide a valid cache path. at E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Compiler.php:36)
[stacktrace]
#0 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\View\\ViewServiceProvider.php(88): Illuminate\\View\\Compilers\\Compiler->__construct(Object(Illuminate\\Filesystem\\Filesystem), false)
#1 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(873): Illuminate\\View\\ViewServiceProvider->Illuminate\\View\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(758): Illuminate\\Container\\Container->build(Object(Closure))
#3 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(853): Illuminate\\Container\\Container->resolve('blade.compiler', Array, true)
#4 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Foundation\\Application->resolve('blade.compiler', Array)
#5 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(838): Illuminate\\Container\\Container->make('blade.compiler', Array)
#6 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1419): Illuminate\\Foundation\\Application->make('blade.compiler')
#7 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\View\\ViewServiceProvider.php(150): Illuminate\\Container\\Container->offsetGet('blade.compiler')
#8 [internal function]: Illuminate\\View\\ViewServiceProvider->Illuminate\\View\\{closure}()
#9 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\EngineResolver.php(55): call_user_func(Object(Closure))
#10 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\facade\\ignition\\src\\IgnitionServiceProvider.php(467): Illuminate\\View\\Engines\\EngineResolver->resolve('blade')
#11 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\facade\\ignition\\src\\IgnitionServiceProvider.php(158): Facade\\Ignition\\IgnitionServiceProvider->hasCustomViewEnginesRegistered()
#12 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\facade\\ignition\\src\\IgnitionServiceProvider.php(101): Facade\\Ignition\\IgnitionServiceProvider->registerViewEngines()
#13 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Facade\\Ignition\\IgnitionServiceProvider->boot()
#14 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#16 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#17 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#18 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(926): Illuminate\\Container\\Container->call(Array)
#19 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(907): Illuminate\\Foundation\\Application->bootProvider(Object(Facade\\Ignition\\IgnitionServiceProvider))
#20 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Facade\\Ignition\\IgnitionServiceProvider), 15)
#21 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(906): array_walk(Array, Object(Closure))
#22 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#23 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(237): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#24 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#25 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#26 E:\\kumaran\\Primus-LandD-Packaging\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 {main}
"} 
[2025-07-30 04:40:59] local.ERROR: Attempt to read property "name" on null {"userId":1,"exception":"[object] (ErrorException(code: 0): Attempt to read property \"name\" on null at E:\\kumaran\\Primus-LandD-Packaging\\app\\Http\\Controllers\\AdminController.php:773)
[stacktrace]
#0 E:\\kumaran\\Primus-LandD-Packaging\\app\\Http\\Controllers\\AdminController.php(773): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Attempt to read...', 'E:\\\\kumaran\\\\Prim...', 773)
#1 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Http\\Controllers\\AdminController->App\\Http\\Controllers\\{closure}(Object(App\\Models\\LearningDevelopmentIdea))
#2 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#3 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#4 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#5 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#6 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\Utilities\\Helper.php(71): Illuminate\\Container\\Container->call(Object(Closure), Array)
#7 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\Processors\\DataProcessor.php(137): Yajra\\DataTables\\Utilities\\Helper::compileContent(Object(Closure), Array, Object(App\\Models\\LearningDevelopmentIdea))
#8 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\Processors\\DataProcessor.php(106): Yajra\\DataTables\\Processors\\DataProcessor->addColumns(Array, Object(App\\Models\\LearningDevelopmentIdea))
#9 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\DataTableAbstract.php(806): Yajra\\DataTables\\Processors\\DataProcessor->process(true)
#10 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\CollectionDataTable.php(164): Yajra\\DataTables\\DataTableAbstract->processResults(Array, true)
#11 E:\\kumaran\\Primus-LandD-Packaging\\app\\Http\\Controllers\\AdminController.php(794): Yajra\\DataTables\\CollectionDataTable->make(true)
#12 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\AdminController->get_learning_devlopment_idea_list(Object(Illuminate\\Http\\Request))
#13 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('get_learning_de...', Array)
#14 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(261): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\AdminController), 'get_learning_de...')
#15 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#16 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#17 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#18 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#28 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(719): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#35 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#36 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#37 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#38 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#39 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#40 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#56 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#57 E:\\kumaran\\Primus-LandD-Packaging\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#58 E:\\kumaran\\Primus-LandD-Packaging\\server.php(21): require_once('E:\\\\kumaran\\\\Prim...')
#59 {main}
"} 
[2025-07-30 04:40:59] local.ERROR: Attempt to read property "name" on null {"userId":1,"exception":"[object] (ErrorException(code: 0): Attempt to read property \"name\" on null at E:\\kumaran\\Primus-LandD-Packaging\\app\\Http\\Controllers\\AdminController.php:773)
[stacktrace]
#0 E:\\kumaran\\Primus-LandD-Packaging\\app\\Http\\Controllers\\AdminController.php(773): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Attempt to read...', 'E:\\\\kumaran\\\\Prim...', 773)
#1 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Http\\Controllers\\AdminController->App\\Http\\Controllers\\{closure}(Object(App\\Models\\LearningDevelopmentIdea))
#2 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#3 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#4 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#5 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#6 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\Utilities\\Helper.php(71): Illuminate\\Container\\Container->call(Object(Closure), Array)
#7 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\Processors\\DataProcessor.php(137): Yajra\\DataTables\\Utilities\\Helper::compileContent(Object(Closure), Array, Object(App\\Models\\LearningDevelopmentIdea))
#8 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\Processors\\DataProcessor.php(106): Yajra\\DataTables\\Processors\\DataProcessor->addColumns(Array, Object(App\\Models\\LearningDevelopmentIdea))
#9 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\DataTableAbstract.php(806): Yajra\\DataTables\\Processors\\DataProcessor->process(true)
#10 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\CollectionDataTable.php(164): Yajra\\DataTables\\DataTableAbstract->processResults(Array, true)
#11 E:\\kumaran\\Primus-LandD-Packaging\\app\\Http\\Controllers\\AdminController.php(794): Yajra\\DataTables\\CollectionDataTable->make(true)
#12 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\AdminController->get_learning_devlopment_idea_list(Object(Illuminate\\Http\\Request))
#13 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('get_learning_de...', Array)
#14 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(261): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\AdminController), 'get_learning_de...')
#15 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#16 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#17 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#18 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#28 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(719): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#35 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#36 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#37 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#38 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#39 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#40 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#56 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#57 E:\\kumaran\\Primus-LandD-Packaging\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#58 E:\\kumaran\\Primus-LandD-Packaging\\server.php(21): require_once('E:\\\\kumaran\\\\Prim...')
#59 {main}
"} 
[2025-07-30 04:42:34] local.ERROR: Object of class stdClass could not be converted to string {"userId":1,"exception":"[object] (Error(code: 0): Object of class stdClass could not be converted to string at E:\\kumaran\\Primus-LandD-Packaging\\app\\Http\\Controllers\\AdminController.php:539)
[stacktrace]
#0 [internal function]: App\\Http\\Controllers\\AdminController->App\\Http\\Controllers\\{closure}(Object(stdClass), 0)
#1 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(695): array_map(Object(Closure), Array, Array)
#2 E:\\kumaran\\Primus-LandD-Packaging\\app\\Http\\Controllers\\AdminController.php(538): Illuminate\\Support\\Collection->map(Object(Closure))
#3 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\AdminController->edit_learning_development(Object(Illuminate\\Http\\Request))
#4 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('edit_learning_d...', Array)
#5 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(261): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\AdminController), 'edit_learning_d...')
#6 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#7 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#8 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#9 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#19 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(719): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#27 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#28 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#29 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#30 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#31 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#47 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#48 E:\\kumaran\\Primus-LandD-Packaging\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#49 E:\\kumaran\\Primus-LandD-Packaging\\server.php(21): require_once('E:\\\\kumaran\\\\Prim...')
#50 {main}
"} 
[2025-07-30 04:48:22] local.ERROR: Attempt to read property "name" on null {"userId":1,"exception":"[object] (ErrorException(code: 0): Attempt to read property \"name\" on null at E:\\kumaran\\Primus-LandD-Packaging\\app\\Http\\Controllers\\AdminController.php:773)
[stacktrace]
#0 E:\\kumaran\\Primus-LandD-Packaging\\app\\Http\\Controllers\\AdminController.php(773): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Attempt to read...', 'E:\\\\kumaran\\\\Prim...', 773)
#1 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Http\\Controllers\\AdminController->App\\Http\\Controllers\\{closure}(Object(App\\Models\\LearningDevelopmentIdea))
#2 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#3 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#4 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#5 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#6 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\Utilities\\Helper.php(71): Illuminate\\Container\\Container->call(Object(Closure), Array)
#7 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\Processors\\DataProcessor.php(137): Yajra\\DataTables\\Utilities\\Helper::compileContent(Object(Closure), Array, Object(App\\Models\\LearningDevelopmentIdea))
#8 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\Processors\\DataProcessor.php(106): Yajra\\DataTables\\Processors\\DataProcessor->addColumns(Array, Object(App\\Models\\LearningDevelopmentIdea))
#9 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\DataTableAbstract.php(806): Yajra\\DataTables\\Processors\\DataProcessor->process(true)
#10 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\CollectionDataTable.php(164): Yajra\\DataTables\\DataTableAbstract->processResults(Array, true)
#11 E:\\kumaran\\Primus-LandD-Packaging\\app\\Http\\Controllers\\AdminController.php(794): Yajra\\DataTables\\CollectionDataTable->make(true)
#12 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\AdminController->get_learning_devlopment_idea_list(Object(Illuminate\\Http\\Request))
#13 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('get_learning_de...', Array)
#14 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(261): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\AdminController), 'get_learning_de...')
#15 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#16 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#17 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#18 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#28 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(719): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#35 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#36 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#37 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#38 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#39 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#40 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#56 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#57 E:\\kumaran\\Primus-LandD-Packaging\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#58 E:\\kumaran\\Primus-LandD-Packaging\\server.php(21): require_once('E:\\\\kumaran\\\\Prim...')
#59 {main}
"} 
[2025-07-30 04:48:24] local.ERROR: Attempt to read property "name" on null {"userId":1,"exception":"[object] (ErrorException(code: 0): Attempt to read property \"name\" on null at E:\\kumaran\\Primus-LandD-Packaging\\app\\Http\\Controllers\\AdminController.php:773)
[stacktrace]
#0 E:\\kumaran\\Primus-LandD-Packaging\\app\\Http\\Controllers\\AdminController.php(773): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Attempt to read...', 'E:\\\\kumaran\\\\Prim...', 773)
#1 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Http\\Controllers\\AdminController->App\\Http\\Controllers\\{closure}(Object(App\\Models\\LearningDevelopmentIdea))
#2 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#3 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#4 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#5 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#6 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\Utilities\\Helper.php(71): Illuminate\\Container\\Container->call(Object(Closure), Array)
#7 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\Processors\\DataProcessor.php(137): Yajra\\DataTables\\Utilities\\Helper::compileContent(Object(Closure), Array, Object(App\\Models\\LearningDevelopmentIdea))
#8 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\Processors\\DataProcessor.php(106): Yajra\\DataTables\\Processors\\DataProcessor->addColumns(Array, Object(App\\Models\\LearningDevelopmentIdea))
#9 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\DataTableAbstract.php(806): Yajra\\DataTables\\Processors\\DataProcessor->process(true)
#10 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\CollectionDataTable.php(164): Yajra\\DataTables\\DataTableAbstract->processResults(Array, true)
#11 E:\\kumaran\\Primus-LandD-Packaging\\app\\Http\\Controllers\\AdminController.php(794): Yajra\\DataTables\\CollectionDataTable->make(true)
#12 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\AdminController->get_learning_devlopment_idea_list(Object(Illuminate\\Http\\Request))
#13 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('get_learning_de...', Array)
#14 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(261): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\AdminController), 'get_learning_de...')
#15 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#16 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#17 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#18 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#28 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(719): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#35 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#36 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#37 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#38 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#39 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#40 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#56 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#57 E:\\kumaran\\Primus-LandD-Packaging\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#58 E:\\kumaran\\Primus-LandD-Packaging\\server.php(21): require_once('E:\\\\kumaran\\\\Prim...')
#59 {main}
"} 
[2025-07-30 04:51:37] local.ERROR: Attempt to read property "name" on null {"userId":1,"exception":"[object] (ErrorException(code: 0): Attempt to read property \"name\" on null at E:\\kumaran\\Primus-LandD-Packaging\\app\\Http\\Controllers\\AdminController.php:773)
[stacktrace]
#0 E:\\kumaran\\Primus-LandD-Packaging\\app\\Http\\Controllers\\AdminController.php(773): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Attempt to read...', 'E:\\\\kumaran\\\\Prim...', 773)
#1 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Http\\Controllers\\AdminController->App\\Http\\Controllers\\{closure}(Object(App\\Models\\LearningDevelopmentIdea))
#2 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#3 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#4 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#5 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#6 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\Utilities\\Helper.php(71): Illuminate\\Container\\Container->call(Object(Closure), Array)
#7 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\Processors\\DataProcessor.php(137): Yajra\\DataTables\\Utilities\\Helper::compileContent(Object(Closure), Array, Object(App\\Models\\LearningDevelopmentIdea))
#8 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\Processors\\DataProcessor.php(106): Yajra\\DataTables\\Processors\\DataProcessor->addColumns(Array, Object(App\\Models\\LearningDevelopmentIdea))
#9 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\DataTableAbstract.php(806): Yajra\\DataTables\\Processors\\DataProcessor->process(true)
#10 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\CollectionDataTable.php(164): Yajra\\DataTables\\DataTableAbstract->processResults(Array, true)
#11 E:\\kumaran\\Primus-LandD-Packaging\\app\\Http\\Controllers\\AdminController.php(794): Yajra\\DataTables\\CollectionDataTable->make(true)
#12 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\AdminController->get_learning_devlopment_idea_list(Object(Illuminate\\Http\\Request))
#13 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('get_learning_de...', Array)
#14 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(261): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\AdminController), 'get_learning_de...')
#15 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#16 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#17 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#18 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#28 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(719): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#35 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#36 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#37 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#38 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#39 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#40 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#56 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#57 E:\\kumaran\\Primus-LandD-Packaging\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#58 E:\\kumaran\\Primus-LandD-Packaging\\server.php(21): require_once('E:\\\\kumaran\\\\Prim...')
#59 {main}
"} 
[2025-07-30 04:51:38] local.ERROR: Attempt to read property "name" on null {"userId":1,"exception":"[object] (ErrorException(code: 0): Attempt to read property \"name\" on null at E:\\kumaran\\Primus-LandD-Packaging\\app\\Http\\Controllers\\AdminController.php:773)
[stacktrace]
#0 E:\\kumaran\\Primus-LandD-Packaging\\app\\Http\\Controllers\\AdminController.php(773): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Attempt to read...', 'E:\\\\kumaran\\\\Prim...', 773)
#1 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Http\\Controllers\\AdminController->App\\Http\\Controllers\\{closure}(Object(App\\Models\\LearningDevelopmentIdea))
#2 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#3 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#4 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#5 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#6 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\Utilities\\Helper.php(71): Illuminate\\Container\\Container->call(Object(Closure), Array)
#7 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\Processors\\DataProcessor.php(137): Yajra\\DataTables\\Utilities\\Helper::compileContent(Object(Closure), Array, Object(App\\Models\\LearningDevelopmentIdea))
#8 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\Processors\\DataProcessor.php(106): Yajra\\DataTables\\Processors\\DataProcessor->addColumns(Array, Object(App\\Models\\LearningDevelopmentIdea))
#9 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\DataTableAbstract.php(806): Yajra\\DataTables\\Processors\\DataProcessor->process(true)
#10 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\CollectionDataTable.php(164): Yajra\\DataTables\\DataTableAbstract->processResults(Array, true)
#11 E:\\kumaran\\Primus-LandD-Packaging\\app\\Http\\Controllers\\AdminController.php(794): Yajra\\DataTables\\CollectionDataTable->make(true)
#12 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\AdminController->get_learning_devlopment_idea_list(Object(Illuminate\\Http\\Request))
#13 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('get_learning_de...', Array)
#14 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(261): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\AdminController), 'get_learning_de...')
#15 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#16 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#17 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#18 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#28 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(719): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#35 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#36 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#37 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#38 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#39 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#40 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#56 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#57 E:\\kumaran\\Primus-LandD-Packaging\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#58 E:\\kumaran\\Primus-LandD-Packaging\\server.php(21): require_once('E:\\\\kumaran\\\\Prim...')
#59 {main}
"} 
[2025-07-30 05:00:53] local.ERROR: Attempt to read property "name" on null {"userId":1,"exception":"[object] (ErrorException(code: 0): Attempt to read property \"name\" on null at E:\\kumaran\\Primus-LandD-Packaging\\app\\Http\\Controllers\\AdminController.php:773)
[stacktrace]
#0 E:\\kumaran\\Primus-LandD-Packaging\\app\\Http\\Controllers\\AdminController.php(773): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Attempt to read...', 'E:\\\\kumaran\\\\Prim...', 773)
#1 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Http\\Controllers\\AdminController->App\\Http\\Controllers\\{closure}(Object(App\\Models\\LearningDevelopmentIdea))
#2 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#3 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#4 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#5 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#6 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\Utilities\\Helper.php(71): Illuminate\\Container\\Container->call(Object(Closure), Array)
#7 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\Processors\\DataProcessor.php(137): Yajra\\DataTables\\Utilities\\Helper::compileContent(Object(Closure), Array, Object(App\\Models\\LearningDevelopmentIdea))
#8 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\Processors\\DataProcessor.php(106): Yajra\\DataTables\\Processors\\DataProcessor->addColumns(Array, Object(App\\Models\\LearningDevelopmentIdea))
#9 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\DataTableAbstract.php(806): Yajra\\DataTables\\Processors\\DataProcessor->process(true)
#10 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\CollectionDataTable.php(164): Yajra\\DataTables\\DataTableAbstract->processResults(Array, true)
#11 E:\\kumaran\\Primus-LandD-Packaging\\app\\Http\\Controllers\\AdminController.php(794): Yajra\\DataTables\\CollectionDataTable->make(true)
#12 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\AdminController->get_learning_devlopment_idea_list(Object(Illuminate\\Http\\Request))
#13 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('get_learning_de...', Array)
#14 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(261): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\AdminController), 'get_learning_de...')
#15 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#16 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#17 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#18 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#28 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(719): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#35 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#36 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#37 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#38 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#39 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#40 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#56 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#57 E:\\kumaran\\Primus-LandD-Packaging\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#58 E:\\kumaran\\Primus-LandD-Packaging\\server.php(21): require_once('E:\\\\kumaran\\\\Prim...')
#59 {main}
"} 
[2025-07-30 05:00:55] local.ERROR: Attempt to read property "name" on null {"userId":1,"exception":"[object] (ErrorException(code: 0): Attempt to read property \"name\" on null at E:\\kumaran\\Primus-LandD-Packaging\\app\\Http\\Controllers\\AdminController.php:773)
[stacktrace]
#0 E:\\kumaran\\Primus-LandD-Packaging\\app\\Http\\Controllers\\AdminController.php(773): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Attempt to read...', 'E:\\\\kumaran\\\\Prim...', 773)
#1 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Http\\Controllers\\AdminController->App\\Http\\Controllers\\{closure}(Object(App\\Models\\LearningDevelopmentIdea))
#2 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#3 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#4 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#5 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#6 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\Utilities\\Helper.php(71): Illuminate\\Container\\Container->call(Object(Closure), Array)
#7 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\Processors\\DataProcessor.php(137): Yajra\\DataTables\\Utilities\\Helper::compileContent(Object(Closure), Array, Object(App\\Models\\LearningDevelopmentIdea))
#8 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\Processors\\DataProcessor.php(106): Yajra\\DataTables\\Processors\\DataProcessor->addColumns(Array, Object(App\\Models\\LearningDevelopmentIdea))
#9 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\DataTableAbstract.php(806): Yajra\\DataTables\\Processors\\DataProcessor->process(true)
#10 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\CollectionDataTable.php(164): Yajra\\DataTables\\DataTableAbstract->processResults(Array, true)
#11 E:\\kumaran\\Primus-LandD-Packaging\\app\\Http\\Controllers\\AdminController.php(794): Yajra\\DataTables\\CollectionDataTable->make(true)
#12 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\AdminController->get_learning_devlopment_idea_list(Object(Illuminate\\Http\\Request))
#13 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('get_learning_de...', Array)
#14 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(261): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\AdminController), 'get_learning_de...')
#15 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#16 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#17 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#18 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#28 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(719): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#35 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#36 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#37 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#38 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#39 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#40 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#56 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#57 E:\\kumaran\\Primus-LandD-Packaging\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#58 E:\\kumaran\\Primus-LandD-Packaging\\server.php(21): require_once('E:\\\\kumaran\\\\Prim...')
#59 {main}
"} 
[2025-07-30 05:01:03] local.ERROR: Attempt to read property "name" on null {"userId":1,"exception":"[object] (ErrorException(code: 0): Attempt to read property \"name\" on null at E:\\kumaran\\Primus-LandD-Packaging\\app\\Http\\Controllers\\AdminController.php:773)
[stacktrace]
#0 E:\\kumaran\\Primus-LandD-Packaging\\app\\Http\\Controllers\\AdminController.php(773): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Attempt to read...', 'E:\\\\kumaran\\\\Prim...', 773)
#1 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Http\\Controllers\\AdminController->App\\Http\\Controllers\\{closure}(Object(App\\Models\\LearningDevelopmentIdea))
#2 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#3 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#4 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#5 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#6 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\Utilities\\Helper.php(71): Illuminate\\Container\\Container->call(Object(Closure), Array)
#7 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\Processors\\DataProcessor.php(137): Yajra\\DataTables\\Utilities\\Helper::compileContent(Object(Closure), Array, Object(App\\Models\\LearningDevelopmentIdea))
#8 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\Processors\\DataProcessor.php(106): Yajra\\DataTables\\Processors\\DataProcessor->addColumns(Array, Object(App\\Models\\LearningDevelopmentIdea))
#9 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\DataTableAbstract.php(806): Yajra\\DataTables\\Processors\\DataProcessor->process(true)
#10 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\CollectionDataTable.php(164): Yajra\\DataTables\\DataTableAbstract->processResults(Array, true)
#11 E:\\kumaran\\Primus-LandD-Packaging\\app\\Http\\Controllers\\AdminController.php(794): Yajra\\DataTables\\CollectionDataTable->make(true)
#12 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\AdminController->get_learning_devlopment_idea_list(Object(Illuminate\\Http\\Request))
#13 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('get_learning_de...', Array)
#14 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(261): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\AdminController), 'get_learning_de...')
#15 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#16 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#17 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#18 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#28 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(719): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#35 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#36 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#37 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#38 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#39 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#40 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#56 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#57 E:\\kumaran\\Primus-LandD-Packaging\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#58 E:\\kumaran\\Primus-LandD-Packaging\\server.php(21): require_once('E:\\\\kumaran\\\\Prim...')
#59 {main}
"} 
[2025-07-30 05:01:04] local.ERROR: Attempt to read property "name" on null {"userId":1,"exception":"[object] (ErrorException(code: 0): Attempt to read property \"name\" on null at E:\\kumaran\\Primus-LandD-Packaging\\app\\Http\\Controllers\\AdminController.php:773)
[stacktrace]
#0 E:\\kumaran\\Primus-LandD-Packaging\\app\\Http\\Controllers\\AdminController.php(773): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Attempt to read...', 'E:\\\\kumaran\\\\Prim...', 773)
#1 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Http\\Controllers\\AdminController->App\\Http\\Controllers\\{closure}(Object(App\\Models\\LearningDevelopmentIdea))
#2 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#3 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#4 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#5 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#6 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\Utilities\\Helper.php(71): Illuminate\\Container\\Container->call(Object(Closure), Array)
#7 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\Processors\\DataProcessor.php(137): Yajra\\DataTables\\Utilities\\Helper::compileContent(Object(Closure), Array, Object(App\\Models\\LearningDevelopmentIdea))
#8 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\Processors\\DataProcessor.php(106): Yajra\\DataTables\\Processors\\DataProcessor->addColumns(Array, Object(App\\Models\\LearningDevelopmentIdea))
#9 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\DataTableAbstract.php(806): Yajra\\DataTables\\Processors\\DataProcessor->process(true)
#10 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\CollectionDataTable.php(164): Yajra\\DataTables\\DataTableAbstract->processResults(Array, true)
#11 E:\\kumaran\\Primus-LandD-Packaging\\app\\Http\\Controllers\\AdminController.php(794): Yajra\\DataTables\\CollectionDataTable->make(true)
#12 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\AdminController->get_learning_devlopment_idea_list(Object(Illuminate\\Http\\Request))
#13 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('get_learning_de...', Array)
#14 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(261): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\AdminController), 'get_learning_de...')
#15 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#16 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#17 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#18 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#28 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(719): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#35 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#36 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#37 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#38 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#39 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#40 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#56 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#57 E:\\kumaran\\Primus-LandD-Packaging\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#58 E:\\kumaran\\Primus-LandD-Packaging\\server.php(21): require_once('E:\\\\kumaran\\\\Prim...')
#59 {main}
"} 
[2025-07-30 05:04:03] local.ERROR: Attempt to read property "name" on null {"userId":1,"exception":"[object] (ErrorException(code: 0): Attempt to read property \"name\" on null at E:\\kumaran\\Primus-LandD-Packaging\\app\\Http\\Controllers\\AdminController.php:773)
[stacktrace]
#0 E:\\kumaran\\Primus-LandD-Packaging\\app\\Http\\Controllers\\AdminController.php(773): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Attempt to read...', 'E:\\\\kumaran\\\\Prim...', 773)
#1 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Http\\Controllers\\AdminController->App\\Http\\Controllers\\{closure}(Object(App\\Models\\LearningDevelopmentIdea))
#2 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#3 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#4 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#5 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#6 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\Utilities\\Helper.php(71): Illuminate\\Container\\Container->call(Object(Closure), Array)
#7 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\Processors\\DataProcessor.php(137): Yajra\\DataTables\\Utilities\\Helper::compileContent(Object(Closure), Array, Object(App\\Models\\LearningDevelopmentIdea))
#8 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\Processors\\DataProcessor.php(106): Yajra\\DataTables\\Processors\\DataProcessor->addColumns(Array, Object(App\\Models\\LearningDevelopmentIdea))
#9 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\DataTableAbstract.php(806): Yajra\\DataTables\\Processors\\DataProcessor->process(true)
#10 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\CollectionDataTable.php(164): Yajra\\DataTables\\DataTableAbstract->processResults(Array, true)
#11 E:\\kumaran\\Primus-LandD-Packaging\\app\\Http\\Controllers\\AdminController.php(794): Yajra\\DataTables\\CollectionDataTable->make(true)
#12 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\AdminController->get_learning_devlopment_idea_list(Object(Illuminate\\Http\\Request))
#13 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('get_learning_de...', Array)
#14 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(261): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\AdminController), 'get_learning_de...')
#15 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#16 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#17 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#18 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#28 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(719): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#35 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#36 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#37 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#38 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#39 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#40 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#56 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#57 E:\\kumaran\\Primus-LandD-Packaging\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#58 E:\\kumaran\\Primus-LandD-Packaging\\server.php(21): require_once('E:\\\\kumaran\\\\Prim...')
#59 {main}
"} 
[2025-07-30 05:04:04] local.ERROR: Attempt to read property "name" on null {"userId":1,"exception":"[object] (ErrorException(code: 0): Attempt to read property \"name\" on null at E:\\kumaran\\Primus-LandD-Packaging\\app\\Http\\Controllers\\AdminController.php:773)
[stacktrace]
#0 E:\\kumaran\\Primus-LandD-Packaging\\app\\Http\\Controllers\\AdminController.php(773): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Attempt to read...', 'E:\\\\kumaran\\\\Prim...', 773)
#1 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Http\\Controllers\\AdminController->App\\Http\\Controllers\\{closure}(Object(App\\Models\\LearningDevelopmentIdea))
#2 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#3 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#4 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#5 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#6 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\Utilities\\Helper.php(71): Illuminate\\Container\\Container->call(Object(Closure), Array)
#7 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\Processors\\DataProcessor.php(137): Yajra\\DataTables\\Utilities\\Helper::compileContent(Object(Closure), Array, Object(App\\Models\\LearningDevelopmentIdea))
#8 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\Processors\\DataProcessor.php(106): Yajra\\DataTables\\Processors\\DataProcessor->addColumns(Array, Object(App\\Models\\LearningDevelopmentIdea))
#9 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\DataTableAbstract.php(806): Yajra\\DataTables\\Processors\\DataProcessor->process(true)
#10 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\CollectionDataTable.php(164): Yajra\\DataTables\\DataTableAbstract->processResults(Array, true)
#11 E:\\kumaran\\Primus-LandD-Packaging\\app\\Http\\Controllers\\AdminController.php(794): Yajra\\DataTables\\CollectionDataTable->make(true)
#12 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\AdminController->get_learning_devlopment_idea_list(Object(Illuminate\\Http\\Request))
#13 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('get_learning_de...', Array)
#14 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(261): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\AdminController), 'get_learning_de...')
#15 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#16 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#17 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#18 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#28 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(719): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#35 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#36 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#37 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#38 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#39 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#40 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#56 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#57 E:\\kumaran\\Primus-LandD-Packaging\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#58 E:\\kumaran\\Primus-LandD-Packaging\\server.php(21): require_once('E:\\\\kumaran\\\\Prim...')
#59 {main}
"} 
[2025-07-30 05:06:15] local.ERROR: Attempt to read property "name" on null {"userId":1,"exception":"[object] (ErrorException(code: 0): Attempt to read property \"name\" on null at E:\\kumaran\\Primus-LandD-Packaging\\app\\Http\\Controllers\\AdminController.php:773)
[stacktrace]
#0 E:\\kumaran\\Primus-LandD-Packaging\\app\\Http\\Controllers\\AdminController.php(773): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Attempt to read...', 'E:\\\\kumaran\\\\Prim...', 773)
#1 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Http\\Controllers\\AdminController->App\\Http\\Controllers\\{closure}(Object(App\\Models\\LearningDevelopmentIdea))
#2 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#3 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#4 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#5 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#6 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\Utilities\\Helper.php(71): Illuminate\\Container\\Container->call(Object(Closure), Array)
#7 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\Processors\\DataProcessor.php(137): Yajra\\DataTables\\Utilities\\Helper::compileContent(Object(Closure), Array, Object(App\\Models\\LearningDevelopmentIdea))
#8 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\Processors\\DataProcessor.php(106): Yajra\\DataTables\\Processors\\DataProcessor->addColumns(Array, Object(App\\Models\\LearningDevelopmentIdea))
#9 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\DataTableAbstract.php(806): Yajra\\DataTables\\Processors\\DataProcessor->process(true)
#10 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\CollectionDataTable.php(164): Yajra\\DataTables\\DataTableAbstract->processResults(Array, true)
#11 E:\\kumaran\\Primus-LandD-Packaging\\app\\Http\\Controllers\\AdminController.php(794): Yajra\\DataTables\\CollectionDataTable->make(true)
#12 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\AdminController->get_learning_devlopment_idea_list(Object(Illuminate\\Http\\Request))
#13 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('get_learning_de...', Array)
#14 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(261): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\AdminController), 'get_learning_de...')
#15 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#16 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#17 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#18 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#28 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(719): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#35 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#36 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#37 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#38 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#39 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#40 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#56 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#57 E:\\kumaran\\Primus-LandD-Packaging\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#58 E:\\kumaran\\Primus-LandD-Packaging\\server.php(21): require_once('E:\\\\kumaran\\\\Prim...')
#59 {main}
"} 
[2025-07-30 05:06:17] local.ERROR: Attempt to read property "name" on null {"userId":1,"exception":"[object] (ErrorException(code: 0): Attempt to read property \"name\" on null at E:\\kumaran\\Primus-LandD-Packaging\\app\\Http\\Controllers\\AdminController.php:773)
[stacktrace]
#0 E:\\kumaran\\Primus-LandD-Packaging\\app\\Http\\Controllers\\AdminController.php(773): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Attempt to read...', 'E:\\\\kumaran\\\\Prim...', 773)
#1 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Http\\Controllers\\AdminController->App\\Http\\Controllers\\{closure}(Object(App\\Models\\LearningDevelopmentIdea))
#2 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#3 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#4 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#5 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#6 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\Utilities\\Helper.php(71): Illuminate\\Container\\Container->call(Object(Closure), Array)
#7 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\Processors\\DataProcessor.php(137): Yajra\\DataTables\\Utilities\\Helper::compileContent(Object(Closure), Array, Object(App\\Models\\LearningDevelopmentIdea))
#8 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\Processors\\DataProcessor.php(106): Yajra\\DataTables\\Processors\\DataProcessor->addColumns(Array, Object(App\\Models\\LearningDevelopmentIdea))
#9 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\DataTableAbstract.php(806): Yajra\\DataTables\\Processors\\DataProcessor->process(true)
#10 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\CollectionDataTable.php(164): Yajra\\DataTables\\DataTableAbstract->processResults(Array, true)
#11 E:\\kumaran\\Primus-LandD-Packaging\\app\\Http\\Controllers\\AdminController.php(794): Yajra\\DataTables\\CollectionDataTable->make(true)
#12 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\AdminController->get_learning_devlopment_idea_list(Object(Illuminate\\Http\\Request))
#13 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('get_learning_de...', Array)
#14 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(261): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\AdminController), 'get_learning_de...')
#15 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#16 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#17 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#18 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#28 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(719): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#35 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#36 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#37 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#38 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#39 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#40 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#56 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#57 E:\\kumaran\\Primus-LandD-Packaging\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#58 E:\\kumaran\\Primus-LandD-Packaging\\server.php(21): require_once('E:\\\\kumaran\\\\Prim...')
#59 {main}
"} 
[2025-07-30 05:10:11] local.ERROR: Attempt to read property "name" on null {"userId":1,"exception":"[object] (ErrorException(code: 0): Attempt to read property \"name\" on null at E:\\kumaran\\Primus-LandD-Packaging\\app\\Http\\Controllers\\AdminController.php:773)
[stacktrace]
#0 E:\\kumaran\\Primus-LandD-Packaging\\app\\Http\\Controllers\\AdminController.php(773): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Attempt to read...', 'E:\\\\kumaran\\\\Prim...', 773)
#1 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Http\\Controllers\\AdminController->App\\Http\\Controllers\\{closure}(Object(App\\Models\\LearningDevelopmentIdea))
#2 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#3 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#4 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#5 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#6 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\Utilities\\Helper.php(71): Illuminate\\Container\\Container->call(Object(Closure), Array)
#7 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\Processors\\DataProcessor.php(137): Yajra\\DataTables\\Utilities\\Helper::compileContent(Object(Closure), Array, Object(App\\Models\\LearningDevelopmentIdea))
#8 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\Processors\\DataProcessor.php(106): Yajra\\DataTables\\Processors\\DataProcessor->addColumns(Array, Object(App\\Models\\LearningDevelopmentIdea))
#9 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\DataTableAbstract.php(806): Yajra\\DataTables\\Processors\\DataProcessor->process(true)
#10 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\CollectionDataTable.php(164): Yajra\\DataTables\\DataTableAbstract->processResults(Array, true)
#11 E:\\kumaran\\Primus-LandD-Packaging\\app\\Http\\Controllers\\AdminController.php(794): Yajra\\DataTables\\CollectionDataTable->make(true)
#12 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\AdminController->get_learning_devlopment_idea_list(Object(Illuminate\\Http\\Request))
#13 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('get_learning_de...', Array)
#14 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(261): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\AdminController), 'get_learning_de...')
#15 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#16 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#17 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#18 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#28 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(719): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#35 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#36 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#37 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#38 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#39 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#40 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#56 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#57 E:\\kumaran\\Primus-LandD-Packaging\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#58 E:\\kumaran\\Primus-LandD-Packaging\\server.php(21): require_once('E:\\\\kumaran\\\\Prim...')
#59 {main}
"} 
[2025-07-30 05:10:12] local.ERROR: Attempt to read property "name" on null {"userId":1,"exception":"[object] (ErrorException(code: 0): Attempt to read property \"name\" on null at E:\\kumaran\\Primus-LandD-Packaging\\app\\Http\\Controllers\\AdminController.php:773)
[stacktrace]
#0 E:\\kumaran\\Primus-LandD-Packaging\\app\\Http\\Controllers\\AdminController.php(773): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Attempt to read...', 'E:\\\\kumaran\\\\Prim...', 773)
#1 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Http\\Controllers\\AdminController->App\\Http\\Controllers\\{closure}(Object(App\\Models\\LearningDevelopmentIdea))
#2 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#3 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(81): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#4 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Object(Closure), Object(Closure))
#5 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Object(Closure), Array, NULL)
#6 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\Utilities\\Helper.php(71): Illuminate\\Container\\Container->call(Object(Closure), Array)
#7 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\Processors\\DataProcessor.php(137): Yajra\\DataTables\\Utilities\\Helper::compileContent(Object(Closure), Array, Object(App\\Models\\LearningDevelopmentIdea))
#8 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\Processors\\DataProcessor.php(106): Yajra\\DataTables\\Processors\\DataProcessor->addColumns(Array, Object(App\\Models\\LearningDevelopmentIdea))
#9 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\DataTableAbstract.php(806): Yajra\\DataTables\\Processors\\DataProcessor->process(true)
#10 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\yajra\\laravel-datatables-oracle\\src\\CollectionDataTable.php(164): Yajra\\DataTables\\DataTableAbstract->processResults(Array, true)
#11 E:\\kumaran\\Primus-LandD-Packaging\\app\\Http\\Controllers\\AdminController.php(794): Yajra\\DataTables\\CollectionDataTable->make(true)
#12 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\AdminController->get_learning_devlopment_idea_list(Object(Illuminate\\Http\\Request))
#13 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('get_learning_de...', Array)
#14 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(261): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\AdminController), 'get_learning_de...')
#15 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#16 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#17 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#18 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#28 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(719): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#35 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#36 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#37 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#38 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#39 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#40 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#56 E:\\kumaran\\Primus-LandD-Packaging\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#57 E:\\kumaran\\Primus-LandD-Packaging\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#58 E:\\kumaran\\Primus-LandD-Packaging\\server.php(21): require_once('E:\\\\kumaran\\\\Prim...')
#59 {main}
"} 
